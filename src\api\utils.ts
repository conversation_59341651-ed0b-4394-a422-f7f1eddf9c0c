export const baseUrlApi = (url: string) => {
  // 如果 url 已经以 api/ 开头，则去掉重复的前缀
  const cleanUrl = url.startsWith("api/") ? url.substring(4) : url;
  return process.env.NODE_ENV === "development"
    ? `/api/${cleanUrl}`
    : `/api/${cleanUrl}`;
};

export const simpleUploadUrl = () => {
  return process.env.NODE_ENV === "development"
    ? "api/api/v1/fs/upload_chunk"
    : "api/v1/fs/upload_chunk";
};

export const downLoadAudioFileUrl =
  process.env.NODE_ENV === "development"
    ? "api/api/v1/voice2text/result/"
    : "api/v1/voice2text/result/";

export const downLoadPdfFileUrl =
  process.env.NODE_ENV === "development"
    ? "api/api/v1/pdf2word/result/"
    : "api/v1/pdf2word/result/";

export const downLoadOfdPdfFileUrl =
  process.env.NODE_ENV === "development"
    ? "api/api/v1/ofd2pdf/result/"
    : "api/v1/ofd2pdf/result/";

export const downLoadOfdWordFileUrl =
  process.env.NODE_ENV === "development"
    ? "api/api/v1/ofd2word/result/"
    : "api/v1/ofd2word/result/";
