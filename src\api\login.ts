import { baseUrlApi } from "./utils";
import { http } from "@/utils/http";

// export function loginApi(data?: object) {
//   return http.request<any>('post', baseUrlApi('api/v1/user/login'), { data })
// }

export function loginApi(data?: object) {
  console.log("当前环境:", process.env.NODE_ENV);
  return http.request<any>("post", baseUrlApi("api/v1/auth/account_login"), {
    data
  });
}

export function loginByAccountApi(data: object) {
  return http.request<any>("post", baseUrlApi("api/v1/auth/account_login"), {
    data
  });
}

// key登录
export function loginByKeyApi() {
  return http.request<any>("post", baseUrlApi("api/v1/auth/key_login"));
}

// ticket登录
export function loginByTicketApi(data: object) {
  return http.request<any>("post", baseUrlApi("api/v1/auth/ticket_login"), {
    data
  });
}

export function getUserProfileApi() {
  return http.request<any>("get", baseUrlApi("api/v1/user/profile"));
}

export function editPasswordApi(data?: object) {
  return http.request<any>("post", baseUrlApi("api/v1/auth/modify_password"), {
    data
  });
}

// 获取微信登录二维码
export function getQrcodeApi() {
  return http.request<any>("get", baseUrlApi("api/v1/login/qrcode"));
}

// 获取扫码结果
export function getScanResultApi(params?: any) {
  return http.request<any>(
    "get",
    baseUrlApi(`api/v1/login/scan_result?ticket=${params.ticket}`)
  );
}

// 获取验证码
export function getSMSCodeApi(data?: object) {
  return http.request<any>("post", baseUrlApi(`api/v1/login/send_sms_code`), {
    data
  });
}

// 手机号登录
export function loginByPhoneApi(data?: object) {
  return http.request<any>("post", baseUrlApi(`api/v1/login/telephone`), {
    data
  });
}

// 绑定手机号登录
export function loginBindPhoneApi(data?: object) {
  return http.request<any>("post", baseUrlApi(`api/v1/login/bind_telephone`), {
    data
  });
}

// 更新用户设置信息
export function updateUserSettingApi(data?: object) {
  return http.request<any>("post", baseUrlApi(`api/v1/user/update_settings`), {
    data
  });
}

// 签到
export function sendDailySignInApi() {
  return http.request<any>("get", baseUrlApi("api/v1/user/daily_sign"));
}

// 获取banner
export function getCommonBannerApi() {
  return http.request<any>("get", baseUrlApi("api/v1/common/banner"));
}

// 许愿
export function makeWishApi(data: object) {
  return http.request<any>("post", baseUrlApi("api/v1/user/make_wish"), {
    data
  });
}

// 获取版本信息
export function getVersionInfoApi() {
  return http.request<any>("get", baseUrlApi("api/v1/version"));
}

// 校验key状态
export function checkUsbKeyStatusApi() {
  return http.request<any>("get", baseUrlApi("api/v1/protect/token"));
}
